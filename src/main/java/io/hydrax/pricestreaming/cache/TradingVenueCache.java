package io.hydrax.pricestreaming.cache;

import io.hydrax.pricestreaming.common.OrderType;
import io.hydrax.pricestreaming.common.TimeInForceEnum;
import io.hydrax.pricestreaming.domain.TradingVenueDTO;
import io.hydrax.proto.metwo.match.TickerRoute;
import io.hydrax.proto.metwo.match.TimeInForce;
import io.hydrax.proto.metwo.match.VenueMarketUpdateRequest;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TradingVenueCache {
  Map<String, TradingVenueDTO> tradingVenues = new ConcurrentHashMap<>();
  Map<String, List<String>> symbolCodes = new ConcurrentHashMap<>();

  // Performance optimization caches
  // Cache for venue codes by timeInForce + orderType + ticker combination
  private final Map<String, List<String>> venueCodesByQuery = new ConcurrentHashMap<>();
  // Cache for venue ticker codes to avoid repeated Set creation
  private final Map<String, Set<String>> venueTickerCodes = new ConcurrentHashMap<>();

  public TradingVenueDTO get(String marketCode, String venueMarketCode) {
    return tradingVenues.get(joinKey(marketCode, venueMarketCode));
  }

  public void put(VenueMarketUpdateRequest request) {
    // Generate a TradingVenue instance based on the request
    TradingVenueDTO venue = TradingVenueDTO.from(request);
    this.put(request.getMarketCode(), request.getCode(), venue);
  }

  public void put(String marketCode, String venueMarketCode, TradingVenueDTO venue) {
    // Update the tradingVenues map with the new or updated TradingVenue data
    tradingVenues.put(joinKey(marketCode, venueMarketCode), venue);

    // Clear performance caches for this venue
    clearCachesForVenue(venueMarketCode);

    // Update the symbolCodes cache
    // Remove any existing cache entries related to the current venue
    symbolCodes.entrySet().removeIf(entry -> entry.getKey().startsWith(venueMarketCode + "_"));

    // Add new symbolCodes data to the cache
    venue
        .getTickersRoute()
        .forEach(
            tickerRoute -> {
              String lpTickerName = tickerRoute.getLpTickerName();
              String cacheKey = venueMarketCode + "_" + lpTickerName;

              // Update the cache with the new ticker codes for the given lpTickerName
              symbolCodes.computeIfAbsent(
                  cacheKey,
                  key ->
                      venue.getTickersRoute().stream()
                          .filter(
                              ticker ->
                                  lpTickerName.equals(
                                      ticker.getLpTickerName())) // Filter by lpTickerName
                          .map(TickerRoute::getTickerCode) // Extract tickerCode
                          .toList() // Collect as an immutable list
                  );
            });

    // Pre-compute and cache venue ticker codes for performance
    cacheVenueTickerCodes(venueMarketCode, venue);
  }

  public List<TradingVenueDTO> getAll() {
    return List.copyOf(tradingVenues.values());
  }

  public void remove(VenueMarketUpdateRequest request) {
    tradingVenues.remove(joinKey(request.getMarketCode(), request.getCode()));
    // Clear performance caches for this venue
    clearCachesForVenue(request.getCode());
  }

  public List<String> selectCodeByTimeInForceAndOrderType(
      String timeInForce, String orderType, String ticker) {
    if (log.isTraceEnabled()) {
      log.trace("timeInForce: {}, orderType: {}, ticker: {}", timeInForce, orderType, ticker);
    }

    // Create cache key for this query
    String queryKey = buildQueryKey(timeInForce, orderType, ticker);

    // Try to get from cache first - this is the main performance optimization
    return venueCodesByQuery.computeIfAbsent(queryKey, key -> {
      if (log.isDebugEnabled()) {
        log.debug("Cache miss for query: {}", queryKey);
      }

      // Convert types once (these methods already use static caches)
      io.hydrax.proto.metwo.match.PsOrderType psOrderType = OrderType.from(orderType);
      TimeInForce timeInForceProto = TimeInForceEnum.from(timeInForce);

      if (psOrderType == null || timeInForceProto == null) {
        log.warn("Invalid orderType: {} or timeInForce: {}", orderType, timeInForce);
        return Collections.emptyList();
      }

      // Perform the expensive computation only on cache miss
      List<String> result = tradingVenues.values().stream()
          .filter(venue -> isVenueMatchingOptimized(venue, psOrderType, timeInForceProto, orderType, ticker))
          .map(TradingVenueDTO::getCode)
          .toList();

      if (log.isDebugEnabled()) {
        log.debug("Computed result for query {}: {} venues", queryKey, result.size());
      }

      return result;
    });
  }

  String joinKey(String marketCode, String venueMarketCode) {
    return marketCode + ":" + venueMarketCode;
  }

  public String getLpTickerName(String marketCode, String venueMarketCode, String symbol) {
    return this.get(marketCode, venueMarketCode).getTickersRoute().stream()
        .filter(tickerRoute -> tickerRoute.getTickerCode().equals(symbol))
        .findFirst()
        .orElse(TickerRoute.newBuilder().build())
        .getLpTickerName();
  }

  public List<String> getMarketCodeByVenueCode(String venueCode) {
    return tradingVenues.values().stream()
        .filter(venue -> venue.getCode().equals(venueCode)) // Filter by code
        .map(TradingVenueDTO::getMarketCode) // Get market code
        .toList();
  }

  public String getLpTickerName(String marketCode, String symbol) {
    return tradingVenues.values().stream()
        .filter(venue -> venue.getMarketCode().equals(marketCode))
        .flatMap(v -> v.getTickersRoute().stream())
        .filter(tickerRoute -> tickerRoute.getTickerCode().equals(symbol))
        .findFirst()
        .orElse(TickerRoute.newBuilder().build())
        .getLpTickerName();
  }

  public List<String> getSymbolCodesByVenueMarketAndVenueSymbol(
      String venueMarketCode, String lpTickerName) {
    // Generate the cache key by combining marketCode and lpTickerName.
    String cacheKey = venueMarketCode + "_" + lpTickerName;

    // Retrieve the ticker codes from the cache if available; otherwise, compute and cache the
    // result.
    return symbolCodes.computeIfAbsent(
        cacheKey,
        key ->
            tradingVenues.values().stream()
                .filter(venue -> venueMarketCode.equals(venue.getCode())) // Filter by marketCode.
                .flatMap(
                    venue -> venue.getTickersRoute().stream()) // Flatten the list of ticker routes.
                .filter(
                    ticker ->
                        lpTickerName.equals(ticker.getLpTickerName())) // Filter by lpTickerName.
                .map(TickerRoute::getTickerCode) // Extract the tickerCode.
                .toList() // Collect as an immutable list.
        );
  }

  // Performance optimization helper methods

  /**
   * Builds a cache key for the query combination
   */
  private String buildQueryKey(String timeInForce, String orderType, String ticker) {
    return timeInForce + "|" + orderType + "|" + ticker;
  }



  /**
   * Checks if a venue matches the given criteria using cached data
   */
  private boolean isVenueMatching(TradingVenueDTO venue,
                                  io.hydrax.proto.metwo.match.PsOrderType psOrderType,
                                  TimeInForce timeInForceProto,
                                  String orderType,
                                  String ticker) {
    // Check if the venue contains the order type
    if (!venue.getOrderTypes().contains(psOrderType)) {
      return false;
    }

    // Check if the venue contains the time in force for this order type
    Set<TimeInForce> timeInForceSet = venue.getTimeInForces().get(orderType);
    if (timeInForceSet == null || !timeInForceSet.contains(timeInForceProto)) {
      return false;
    }

    // Check if the venue contains the ticker code using cached ticker codes
    Set<String> tickerCodes = getCachedVenueTickerCodes(venue.getCode(), venue);
    return tickerCodes.contains(ticker);
  }

  /**
   * Gets cached venue ticker codes, computing if not present
   */
  private Set<String> getCachedVenueTickerCodes(String venueCode, TradingVenueDTO venue) {
    return venueTickerCodes.computeIfAbsent(venueCode, key -> {
      return Optional.ofNullable(venue.getTickersRoute())
          .orElse(Collections.emptyList())
          .stream()
          .map(TickerRoute::getTickerCode)
          .collect(Collectors.toSet());
    });
  }

  /**
   * Pre-computes and caches venue ticker codes for performance
   */
  private void cacheVenueTickerCodes(String venueCode, TradingVenueDTO venue) {
    Set<String> tickerCodes = Optional.ofNullable(venue.getTickersRoute())
        .orElse(Collections.emptyList())
        .stream()
        .map(TickerRoute::getTickerCode)
        .collect(Collectors.toSet());
    venueTickerCodes.put(venueCode, tickerCodes);
  }

  /**
   * Clears all performance caches related to a specific venue
   */
  private void clearCachesForVenue(String venueCode) {
    // Clear venue-specific caches
    venueTickerCodes.remove(venueCode);

    // Clear query caches that might be affected by this venue
    venueCodesByQuery.clear(); // Simple approach - clear all query cache
    // Alternative: more granular clearing based on venue involvement
  }
}
